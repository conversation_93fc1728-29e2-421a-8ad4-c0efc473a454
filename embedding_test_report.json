{"test_summary": {"total_queries": 5, "avg_precision": 0.08, "avg_recall": 0.4, "avg_f1": 0.13333333333333336, "avg_mrr": 0.4, "avg_search_time": 0.026674938201904298}, "detailed_results": [{"query": "机器学习算法", "expected_docs": ["机器学习是人工智能的一个重要子集"], "retrieved_docs": ["深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。", "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。", "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。", "计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。", "自然语言处理（NLP）是人工智能的一个领域，专注于计算机与人类语言之间的交互。"], "distances": [242.6006317138672, 273.0762939453125, 274.38946533203125, 360.75933837890625, 389.4264831542969], "precision": 0.0, "recall": 0.0, "f1": 0, "mrr": 0, "search_time": 0.024425268173217773}, {"query": "深度学习神经网络", "expected_docs": ["深度学习是机器学习的一个分支"], "retrieved_docs": ["深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。", "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。", "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。", "计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。", "自然语言处理（NLP）是人工智能的一个领域，专注于计算机与人类语言之间的交互。"], "distances": [129.08482360839844, 294.25030517578125, 305.6795654296875, 335.4173583984375, 401.2440490722656], "precision": 0.2, "recall": 1.0, "f1": 0.33333333333333337, "mrr": 1.0, "search_time": 0.0253751277923584}, {"query": "自然语言处理", "expected_docs": ["自然语言处理（Natural Language Processing，NLP）"], "retrieved_docs": ["计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。", "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。", "自然语言处理（NLP）是人工智能的一个领域，专注于计算机与人类语言之间的交互。", "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。", "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。"], "distances": [304.42138671875, 315.679443359375, 322.66259765625, 324.04779052734375, 341.02691650390625], "precision": 0.0, "recall": 0.0, "f1": 0, "mrr": 0, "search_time": 0.02605724334716797}, {"query": "计算机视觉", "expected_docs": ["计算机视觉是人工智能的一个分支"], "retrieved_docs": ["计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。", "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。", "自然语言处理（NLP）是人工智能的一个领域，专注于计算机与人类语言之间的交互。", "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。", "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。"], "distances": [309.4571838378906, 316.09356689453125, 325.662841796875, 327.99163818359375, 343.5649108886719], "precision": 0.2, "recall": 1.0, "f1": 0.33333333333333337, "mrr": 1.0, "search_time": 0.024838924407958984}, {"query": "人工智能应用", "expected_docs": ["人工智能（Artificial Intelligence，AI）"], "retrieved_docs": ["人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。", "计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。", "自然语言处理（NLP）是人工智能的一个领域，专注于计算机与人类语言之间的交互。", "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。", "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。"], "distances": [140.111572265625, 149.81471252441406, 167.0430908203125, 212.2677001953125, 339.3204345703125], "precision": 0.0, "recall": 0.0, "f1": 0, "mrr": 0, "search_time": 0.03267812728881836}]}