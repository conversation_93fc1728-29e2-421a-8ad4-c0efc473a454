# Embedding模型学习项目

这个项目演示如何使用Ollama部署embedding模型，并进行文档索引和召回测试。

## 环境要求

- macOS
- Python 3.8+
- Ollama

## 安装步骤

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 确保Ollama已安装并运行：
```bash
ollama serve
```

3. 下载embedding模型：
```bash
ollama pull nomic-embed-text
```

## 项目结构

```
.
├── README.md
├── requirements.txt
├── config.py              # 配置文件
├── embedding_service.py   # Ollama embedding服务接口
├── vector_store.py        # 向量数据库操作
├── retrieval_test.py      # 召回测试功能
├── main.py               # 主程序入口
├── data/                 # 测试数据目录
│   └── sample_docs.txt
└── examples/             # 使用示例
    └── basic_usage.py
```

## 使用方法

运行基本示例：
```bash
python main.py
```

运行召回测试：
```bash
python retrieval_test.py
```
