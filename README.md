# Embedding模型学习项目

这个项目演示如何使用Ollama部署embedding模型，并进行文档索引和召回测试。

## 环境要求

- macOS
- Python 3.8+
- Ollama

## 安装步骤

1. 安装Python依赖：
```bash
pip install -r requirements.txt
```

2. 确保Ollama已安装并运行：
```bash
ollama serve
```

3. 下载embedding模型：
```bash
ollama pull nomic-embed-text
```

## 项目结构

```
.
├── README.md
├── requirements.txt
├── config.py              # 配置文件
├── embedding_service.py   # Ollama embedding服务接口
├── vector_store.py        # 向量数据库操作
├── retrieval_test.py      # 召回测试功能
├── main.py               # 主程序入口
├── data/                 # 测试数据目录
│   └── sample_docs.txt
├── examples/             # 使用示例
│   └── basic_usage.py
├── quantized_ann.py          # 量化ANN算法实现
├── algorithm_comparison.py   # 算法性能对比测试
├── integrated_ann_test.py    # 集成ANN测试
├── overlap_calculation_demo.py # 重叠度计算演示
├── generate_clean_plots.py   # 清晰图表生成器
└── *.png                    # 生成的性能图表
```

## 使用方法

运行基本示例：
```bash
python main.py
```

运行召回测试：
```bash
python retrieval_test.py
```

运行ANN算法对比测试：
```bash
python quantized_ann.py
python algorithm_comparison.py
python integrated_ann_test.py
```

生成清晰的性能图表（无乱码）：
```bash
python generate_clean_plots.py
```

## ANN算法特性

本项目实现了基于量化的近似最近邻（ANN）搜索算法：

### 🚀 性能提升
- **平均加速比**: 1.95x
- **最大加速比**: 3.16x
- **数据规模越大效果越明显**

### 🎯 质量保证
- **平均精确度**: 94.2%
- **结果重叠度**: 94.2%
- **保持高质量的同时显著提升速度**

### 🔧 技术特点
- **量化技术**: 将高维向量压缩为低维表示
- **两阶段搜索**: 量化粗筛 + 精确计算
- **智能候选筛选**: 通过聚类快速排除不相关向量
- **无缝集成**: 可选择性启用ANN加速

### 📊 图表说明
- `algorithm_comparison_clean.png`: 详细性能对比（无乱码版本）
- `algorithm_comparison_plots.png`: 原始对比图表
- `algorithm_summary_clean.png`: 汇总性能指标
