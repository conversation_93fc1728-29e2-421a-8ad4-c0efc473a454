"""
基于量化的近似最近邻（ANN）搜索算法实现
使用向量量化技术来加速相似性搜索
"""
import numpy as np
import time
from typing import List, Tuple, Dict, Any, Optional
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import pickle
import os
from embedding_service import OllamaEmbeddingService, cosine_similarity


class VectorQuantizer:
    """向量量化器 - 将高维向量压缩为低维表示"""
    
    def __init__(self, n_clusters: int = 256, n_bits: int = 8):
        """
        初始化量化器
        
        Args:
            n_clusters: 聚类中心数量
            n_bits: 量化位数
        """
        self.n_clusters = n_clusters
        self.n_bits = n_bits
        self.kmeans = KMeans(n_clusters=n_clusters, random_state=42, n_init=10)
        self.scaler = StandardScaler()
        self.is_fitted = False
        
    def fit(self, vectors: np.ndarray):
        """
        训练量化器
        
        Args:
            vectors: 训练向量矩阵 (n_samples, n_features)
        """
        print(f"训练量化器，向量数量: {len(vectors)}, 维度: {vectors.shape[1]}")
        
        # 标准化
        vectors_scaled = self.scaler.fit_transform(vectors)
        
        # K-means聚类
        self.kmeans.fit(vectors_scaled)
        self.is_fitted = True
        
        print(f"量化器训练完成，聚类中心数量: {self.n_clusters}")
    
    def quantize(self, vectors: np.ndarray) -> np.ndarray:
        """
        量化向量
        
        Args:
            vectors: 输入向量矩阵
            
        Returns:
            量化后的索引数组
        """
        if not self.is_fitted:
            raise ValueError("量化器未训练，请先调用fit方法")
        
        vectors_scaled = self.scaler.transform(vectors)
        quantized_indices = self.kmeans.predict(vectors_scaled)
        return quantized_indices
    
    def get_cluster_centers(self) -> np.ndarray:
        """获取聚类中心"""
        if not self.is_fitted:
            raise ValueError("量化器未训练")
        return self.kmeans.cluster_centers_
    
    def save(self, filepath: str):
        """保存量化器"""
        with open(filepath, 'wb') as f:
            pickle.dump({
                'kmeans': self.kmeans,
                'scaler': self.scaler,
                'n_clusters': self.n_clusters,
                'n_bits': self.n_bits,
                'is_fitted': self.is_fitted
            }, f)
    
    def load(self, filepath: str):
        """加载量化器"""
        with open(filepath, 'rb') as f:
            data = pickle.load(f)
            self.kmeans = data['kmeans']
            self.scaler = data['scaler']
            self.n_clusters = data['n_clusters']
            self.n_bits = data['n_bits']
            self.is_fitted = data['is_fitted']


class QuantizedANNIndex:
    """基于量化的ANN索引"""
    
    def __init__(self, quantizer: VectorQuantizer, top_k_candidates: int = 50):
        """
        初始化ANN索引
        
        Args:
            quantizer: 向量量化器
            top_k_candidates: 候选向量数量
        """
        self.quantizer = quantizer
        self.top_k_candidates = top_k_candidates
        self.vectors = []  # 原始向量
        self.quantized_indices = []  # 量化索引
        self.metadata = []  # 元数据
        self.cluster_to_vectors = {}  # 聚类到向量的映射
        
    def add_vectors(self, vectors: List[np.ndarray], metadata: List[Dict] = None):
        """
        添加向量到索引
        
        Args:
            vectors: 向量列表
            metadata: 元数据列表
        """
        if metadata is None:
            metadata = [{}] * len(vectors)
        
        # 转换为numpy数组
        vectors_array = np.array(vectors)
        
        # 量化向量
        quantized = self.quantizer.quantize(vectors_array)
        
        # 存储数据
        start_idx = len(self.vectors)
        self.vectors.extend(vectors)
        self.quantized_indices.extend(quantized)
        self.metadata.extend(metadata)
        
        # 更新聚类映射
        for i, cluster_id in enumerate(quantized):
            if cluster_id not in self.cluster_to_vectors:
                self.cluster_to_vectors[cluster_id] = []
            self.cluster_to_vectors[cluster_id].append(start_idx + i)
        
        print(f"添加了 {len(vectors)} 个向量到ANN索引")
    
    def search(self, query_vector: np.ndarray, k: int = 5) -> Tuple[List[int], List[float], float]:
        """
        搜索最相似的向量
        
        Args:
            query_vector: 查询向量
            k: 返回结果数量
            
        Returns:
            (索引列表, 相似度列表, 搜索时间)
        """
        start_time = time.time()
        
        # 1. 量化查询向量
        query_quantized = self.quantizer.quantize(query_vector.reshape(1, -1))[0]
        
        # 2. 找到最相似的聚类中心
        cluster_centers = self.quantizer.get_cluster_centers()
        query_scaled = self.quantizer.scaler.transform(query_vector.reshape(1, -1))[0]
        
        # 计算与所有聚类中心的距离
        cluster_distances = []
        for i, center in enumerate(cluster_centers):
            dist = np.linalg.norm(query_scaled - center)
            cluster_distances.append((i, dist))
        
        # 按距离排序
        cluster_distances.sort(key=lambda x: x[1])
        
        # 3. 从最近的聚类中收集候选向量
        candidates = []
        for cluster_id, _ in cluster_distances:
            if cluster_id in self.cluster_to_vectors:
                candidates.extend(self.cluster_to_vectors[cluster_id])
            if len(candidates) >= self.top_k_candidates:
                break
        
        # 限制候选数量
        candidates = candidates[:self.top_k_candidates]
        
        # 4. 对候选向量进行精确相似度计算
        similarities = []
        for idx in candidates:
            sim = cosine_similarity(query_vector.tolist(), self.vectors[idx].tolist())
            similarities.append((idx, sim))
        
        # 5. 按相似度排序并返回top-k
        similarities.sort(key=lambda x: x[1], reverse=True)
        top_k = similarities[:k]
        
        indices = [idx for idx, _ in top_k]
        scores = [sim for _, sim in top_k]
        
        search_time = time.time() - start_time
        
        return indices, scores, search_time


class LinearSearchIndex:
    """线性搜索索引（用于对比）"""
    
    def __init__(self):
        self.vectors = []
        self.metadata = []
    
    def add_vectors(self, vectors: List[np.ndarray], metadata: List[Dict] = None):
        """添加向量"""
        if metadata is None:
            metadata = [{}] * len(vectors)
        
        self.vectors.extend(vectors)
        self.metadata.extend(metadata)
        
        print(f"添加了 {len(vectors)} 个向量到线性索引")
    
    def search(self, query_vector: np.ndarray, k: int = 5) -> Tuple[List[int], List[float], float]:
        """线性搜索"""
        start_time = time.time()
        
        similarities = []
        for i, vector in enumerate(self.vectors):
            sim = cosine_similarity(query_vector.tolist(), vector.tolist())
            similarities.append((i, sim))
        
        # 排序并返回top-k
        similarities.sort(key=lambda x: x[1], reverse=True)
        top_k = similarities[:k]
        
        indices = [idx for idx, _ in top_k]
        scores = [sim for _, sim in top_k]
        
        search_time = time.time() - start_time
        
        return indices, scores, search_time


def create_test_data(embedding_service: OllamaEmbeddingService, num_docs: int = 100) -> Tuple[List[np.ndarray], List[str]]:
    """
    创建测试数据
    
    Args:
        embedding_service: embedding服务
        num_docs: 文档数量
        
    Returns:
        (向量列表, 文档列表)
    """
    print(f"生成 {num_docs} 个测试文档...")
    
    # 生成多样化的测试文档
    topics = [
        "人工智能和机器学习",
        "深度学习和神经网络", 
        "自然语言处理技术",
        "计算机视觉应用",
        "数据科学和分析",
        "云计算和分布式系统",
        "网络安全和隐私保护",
        "移动应用开发",
        "Web前端技术",
        "数据库设计和优化"
    ]
    
    documents = []
    for i in range(num_docs):
        topic = topics[i % len(topics)]
        doc = f"这是第{i+1}个关于{topic}的技术文档。它详细介绍了相关的概念、方法和应用场景。"
        documents.append(doc)
    
    # 生成embeddings
    print("生成embedding向量...")
    embeddings = embedding_service.get_embeddings_batch(documents)
    
    # 转换为numpy数组
    vectors = [np.array(emb) for emb in embeddings]
    
    return vectors, documents


if __name__ == "__main__":
    # 测试量化ANN算法
    print("=== 量化ANN算法测试 ===")
    
    # 初始化embedding服务
    embedding_service = OllamaEmbeddingService()
    
    if not embedding_service.test_connection():
        print("❌ Ollama连接失败")
        exit(1)
    
    # 创建测试数据
    vectors, documents = create_test_data(embedding_service, num_docs=200)
    
    if not vectors:
        print("❌ 生成测试数据失败")
        exit(1)
    
    print(f"✅ 生成了 {len(vectors)} 个向量，维度: {len(vectors[0])}")
    
    # 训练量化器
    print("\n训练量化器...")
    quantizer = VectorQuantizer(n_clusters=32, n_bits=8)
    quantizer.fit(np.array(vectors))
    
    # 创建索引
    print("\n创建索引...")
    ann_index = QuantizedANNIndex(quantizer, top_k_candidates=30)
    linear_index = LinearSearchIndex()
    
    # 添加向量
    ann_index.add_vectors(vectors)
    linear_index.add_vectors(vectors)
    
    # 测试查询
    test_queries = [
        "机器学习算法的应用",
        "深度学习神经网络",
        "数据库优化技术"
    ]
    
    print("\n=== 性能对比测试 ===")
    
    for query_text in test_queries:
        print(f"\n查询: {query_text}")
        
        # 生成查询向量
        query_embedding = embedding_service.get_embedding(query_text)
        if not query_embedding:
            continue
        
        query_vector = np.array(query_embedding)
        
        # ANN搜索
        ann_indices, ann_scores, ann_time = ann_index.search(query_vector, k=5)
        
        # 线性搜索
        linear_indices, linear_scores, linear_time = linear_index.search(query_vector, k=5)
        
        # 输出结果
        print(f"ANN搜索时间: {ann_time:.4f}秒")
        print(f"线性搜索时间: {linear_time:.4f}秒")
        print(f"加速比: {linear_time/ann_time:.2f}x")
        
        # 计算结果重叠度
        overlap = len(set(ann_indices) & set(linear_indices))
        print(f"结果重叠度: {overlap}/5 ({overlap/5*100:.1f}%)")
        
        print("ANN结果:")
        for i, (idx, score) in enumerate(zip(ann_indices, ann_scores)):
            print(f"  {i+1}. 相似度: {score:.4f} | {documents[idx][:50]}...")
        
        print("线性搜索结果:")
        for i, (idx, score) in enumerate(zip(linear_indices, linear_scores)):
            print(f"  {i+1}. 相似度: {score:.4f} | {documents[idx][:50]}...")
