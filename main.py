"""
Embedding模型学习项目主程序
演示完整的文档索引和召回流程
"""
import os
from typing import List
from vector_store import VectorStore, split_text_into_chunks
from embedding_service import OllamaEmbeddingService
from retrieval_test import RetrievalTester


def load_sample_documents(file_path: str = "data/sample_docs.txt") -> List[str]:
    """
    加载示例文档
    
    Args:
        file_path: 文档文件路径
        
    Returns:
        文档段落列表
    """
    if not os.path.exists(file_path):
        print(f"文档文件不存在: {file_path}")
        return []
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 按段落分割文档
    paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
    
    # 进一步分割长段落
    chunks = []
    for paragraph in paragraphs:
        if len(paragraph) > 300:
            # 分割长段落
            paragraph_chunks = split_text_into_chunks(paragraph, chunk_size=300, overlap=50)
            chunks.extend(paragraph_chunks)
        else:
            chunks.append(paragraph)
    
    return chunks


def interactive_search(vector_store: VectorStore):
    """
    交互式搜索功能
    
    Args:
        vector_store: 向量存储实例
    """
    print("\n=== 交互式搜索 ===")
    print("输入查询文本进行搜索，输入 'quit' 退出")
    
    while True:
        query = input("\n请输入查询: ").strip()
        
        if query.lower() in ['quit', 'exit', '退出']:
            break
        
        if not query:
            continue
        
        print(f"\n搜索: {query}")
        results = vector_store.search(query, n_results=3)
        
        if results["documents"] and results["documents"][0]:
            print("搜索结果:")
            for i, (doc, distance) in enumerate(zip(results["documents"][0], results["distances"][0])):
                similarity = 1 / (1 + abs(distance))  # 转换为相似度
                print(f"\n{i+1}. 相似度: {similarity:.4f}")
                print(f"   内容: {doc[:200]}{'...' if len(doc) > 200 else ''}")
        else:
            print("未找到相关结果")


def main():
    """主函数"""
    print("=== Embedding模型学习项目 ===")
    
    # 1. 测试Ollama连接
    print("\n1. 测试Ollama连接...")
    embedding_service = OllamaEmbeddingService()
    
    if not embedding_service.test_connection():
        print("❌ Ollama连接失败！请确保Ollama服务正在运行")
        print("启动命令: ollama serve")
        return
    
    print("✅ Ollama连接成功!")
    
    # 获取模型信息
    model_info = embedding_service.get_model_info()
    if model_info:
        print(f"模型: {model_info.get('name', 'Unknown')}")
        print(f"大小: {model_info.get('size', 'Unknown')} bytes")
    
    # 2. 初始化向量存储
    print("\n2. 初始化向量数据库...")
    vector_store = VectorStore()
    
    # 获取当前集合信息
    info = vector_store.get_collection_info()
    print(f"集合: {info['name']}")
    print(f"现有文档数量: {info['count']}")
    
    # 3. 加载和索引文档
    print("\n3. 加载示例文档...")
    documents = load_sample_documents()
    
    if not documents:
        print("❌ 未能加载文档")
        return
    
    print(f"加载了 {len(documents)} 个文档段落")
    
    # 如果集合为空，添加文档
    if info['count'] == 0:
        print("\n4. 索引文档到向量数据库...")
        doc_ids = vector_store.add_documents(documents)
        print(f"成功索引 {len(doc_ids)} 个文档")
    else:
        print(f"\n4. 使用现有的 {info['count']} 个文档")
    
    # 5. 演示基本搜索
    print("\n5. 演示基本搜索功能...")
    demo_queries = [
        "什么是机器学习？",
        "深度学习的应用",
        "自然语言处理技术",
        "人工智能在医疗领域的应用"
    ]
    
    for query in demo_queries:
        print(f"\n查询: {query}")
        results = vector_store.search(query, n_results=2)
        
        if results["documents"] and results["documents"][0]:
            for i, (doc, distance) in enumerate(zip(results["documents"][0], results["distances"][0])):
                similarity = 1 / (1 + abs(distance))
                print(f"  {i+1}. 相似度: {similarity:.4f}")
                print(f"     内容: {doc[:150]}{'...' if len(doc) > 150 else ''}")
    
    # 6. 运行召回测试
    print("\n6. 运行召回测试...")
    tester = RetrievalTester(vector_store)
    
    test_queries = [
        "机器学习算法",
        "深度学习神经网络", 
        "自然语言处理",
        "计算机视觉",
        "人工智能应用"
    ]
    
    expected_results = [
        ["机器学习是人工智能的一个重要子集"],
        ["深度学习是机器学习的一个分支"],
        ["自然语言处理（Natural Language Processing，NLP）"],
        ["计算机视觉是人工智能的一个分支"],
        ["人工智能（Artificial Intelligence，AI）"]
    ]
    
    metrics = tester.evaluate_retrieval(test_queries, expected_results)
    
    print("\n=== 召回测试结果 ===")
    print(f"平均精确率: {metrics['avg_precision']:.4f}")
    print(f"平均召回率: {metrics['avg_recall']:.4f}")
    print(f"平均F1分数: {metrics['avg_f1']:.4f}")
    print(f"平均MRR: {metrics['avg_mrr']:.4f}")
    
    # 7. 性能测试
    print("\n7. 性能测试...")
    perf_results = tester.performance_test(test_queries[:3])  # 使用前3个查询进行性能测试
    
    for batch_size, stats in perf_results.items():
        print(f"{batch_size}: {stats['queries_per_second']:.2f} 查询/秒")
    
    # 8. 生成测试报告
    print("\n8. 生成测试报告...")
    tester.generate_report("embedding_test_report.json")
    
    # 9. 交互式搜索
    try:
        interactive_search(vector_store)
    except KeyboardInterrupt:
        print("\n\n程序结束")
    
    print("\n=== 项目演示完成 ===")
    print("你可以:")
    print("1. 查看生成的测试报告: embedding_test_report.json")
    print("2. 重新运行 python main.py 进行更多测试")
    print("3. 运行 python retrieval_test.py 进行详细的召回测试")
    print("4. 修改 data/sample_docs.txt 添加更多文档")


if __name__ == "__main__":
    main()
