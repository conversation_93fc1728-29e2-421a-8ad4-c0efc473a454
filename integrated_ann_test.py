"""
集成ANN算法的完整测试
演示在现有向量存储系统中使用ANN加速搜索
"""
import time
import json
from typing import List, Dict, Any
from vector_store import VectorStore
from embedding_service import OllamaEmbeddingService


def load_test_documents() -> List[str]:
    """加载测试文档"""
    try:
        with open("data/sample_docs.txt", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 按段落分割
        paragraphs = [p.strip() for p in content.split('\n\n') if p.strip()]
        
        # 添加更多测试文档
        additional_docs = [
            "Python是一种高级编程语言，广泛应用于数据科学、机器学习和Web开发。",
            "JavaScript是Web前端开发的核心语言，也可用于后端开发。",
            "Java是一种面向对象的编程语言，具有跨平台特性。",
            "C++是一种系统级编程语言，常用于游戏开发和系统软件。",
            "Go语言是Google开发的编程语言，以并发性能著称。",
            "Rust是一种系统编程语言，注重内存安全和性能。",
            "Swift是苹果开发的编程语言，主要用于iOS和macOS开发。",
            "Kotlin是JetBrains开发的编程语言，可与Java互操作。",
            "TypeScript是JavaScript的超集，添加了静态类型检查。",
            "PHP是一种服务器端脚本语言，广泛用于Web开发。",
            "Ruby是一种动态编程语言，以简洁优雅著称。",
            "Scala是一种多范式编程语言，运行在JVM上。",
            "R语言专门用于统计计算和数据分析。",
            "MATLAB是一种数值计算环境，广泛用于工程和科学计算。",
            "SQL是结构化查询语言，用于数据库操作。"
        ]
        
        return paragraphs + additional_docs
        
    except FileNotFoundError:
        print("未找到sample_docs.txt，使用默认测试文档")
        return additional_docs


def run_performance_comparison(chroma_store: VectorStore, ann_store: VectorStore, queries: List[str]) -> Dict[str, Any]:
    """运行性能对比测试"""
    print("\n=== 性能对比测试 ===")
    
    results = {
        "chroma_results": [],
        "ann_results": [],
        "comparison": {}
    }
    
    total_chroma_time = 0
    total_ann_time = 0
    
    for i, query in enumerate(queries):
        print(f"\n查询 {i+1}: {query}")
        
        # ChromaDB搜索
        chroma_result = chroma_store.search(query, n_results=5)
        chroma_time = chroma_result.get("search_time", 0)
        total_chroma_time += chroma_time
        
        # ANN搜索
        ann_result = ann_store.search(query, n_results=5)
        ann_time = ann_result.get("search_time", 0)
        total_ann_time += ann_time
        
        # 计算结果重叠度
        chroma_docs = set(chroma_result["documents"][0]) if chroma_result["documents"] else set()
        ann_docs = set(ann_result["documents"][0]) if ann_result["documents"] else set()
        overlap = len(chroma_docs & ann_docs) / max(len(chroma_docs), 1)
        
        print(f"  ChromaDB时间: {chroma_time:.4f}秒")
        print(f"  ANN时间: {ann_time:.4f}秒")
        print(f"  加速比: {chroma_time/ann_time:.2f}x" if ann_time > 0 else "N/A")
        print(f"  结果重叠度: {overlap:.2f}")
        
        # 存储结果
        results["chroma_results"].append({
            "query": query,
            "time": chroma_time,
            "documents": chroma_result["documents"][0] if chroma_result["documents"] else []
        })
        
        results["ann_results"].append({
            "query": query,
            "time": ann_time,
            "documents": ann_result["documents"][0] if ann_result["documents"] else [],
            "overlap": overlap
        })
    
    # 计算总体统计
    avg_chroma_time = total_chroma_time / len(queries)
    avg_ann_time = total_ann_time / len(queries)
    overall_speedup = avg_chroma_time / avg_ann_time if avg_ann_time > 0 else 0
    
    results["comparison"] = {
        "total_chroma_time": total_chroma_time,
        "total_ann_time": total_ann_time,
        "avg_chroma_time": avg_chroma_time,
        "avg_ann_time": avg_ann_time,
        "overall_speedup": overall_speedup,
        "num_queries": len(queries)
    }
    
    return results


def demonstrate_search_methods(store: VectorStore, query: str):
    """演示不同搜索方法"""
    print(f"\n=== 搜索方法对比: {query} ===")
    
    # 强制使用ChromaDB搜索
    chroma_result = store.search(query, n_results=3, use_ann=False)
    print(f"ChromaDB搜索时间: {chroma_result.get('search_time', 0):.4f}秒")
    print("ChromaDB结果:")
    for i, doc in enumerate(chroma_result["documents"][0][:3]):
        print(f"  {i+1}. {doc[:100]}...")
    
    # 强制使用ANN搜索
    ann_result = store.search(query, n_results=3, use_ann=True)
    print(f"\nANN搜索时间: {ann_result.get('search_time', 0):.4f}秒")
    print("ANN结果:")
    for i, doc in enumerate(ann_result["documents"][0][:3]):
        print(f"  {i+1}. {doc[:100]}...")
    
    # 计算加速比
    chroma_time = chroma_result.get('search_time', 0)
    ann_time = ann_result.get('search_time', 0)
    if ann_time > 0:
        speedup = chroma_time / ann_time
        print(f"\n加速比: {speedup:.2f}x")


def main():
    """主函数"""
    print("=== 集成ANN算法测试 ===")
    
    # 检查Ollama连接
    embedding_service = OllamaEmbeddingService()
    if not embedding_service.test_connection():
        print("❌ Ollama连接失败")
        return
    
    print("✅ Ollama连接成功")
    
    # 加载测试文档
    print("\n加载测试文档...")
    documents = load_test_documents()
    print(f"加载了 {len(documents)} 个文档")
    
    # 创建两个向量存储实例
    print("\n创建向量存储实例...")
    
    # 标准ChromaDB存储
    chroma_store = VectorStore(collection_name="chroma_test", use_ann=False)
    chroma_store.clear_collection()
    
    # ANN加速存储
    ann_store = VectorStore(collection_name="ann_test", use_ann=True)
    ann_store.clear_collection()
    
    # 添加文档到两个存储
    print("\n索引文档...")
    chroma_ids = chroma_store.add_documents(documents)
    ann_ids = ann_store.add_documents(documents)
    
    print(f"ChromaDB存储: {len(chroma_ids)} 个文档")
    print(f"ANN存储: {len(ann_ids)} 个文档")
    
    # 测试查询
    test_queries = [
        "机器学习算法的应用",
        "编程语言的特点",
        "数据科学和分析",
        "人工智能技术发展",
        "Web开发技术栈"
    ]
    
    # 演示搜索方法对比
    demonstrate_search_methods(ann_store, "机器学习和深度学习")
    
    # 运行性能对比
    performance_results = run_performance_comparison(chroma_store, ann_store, test_queries)
    
    # 输出汇总结果
    print("\n" + "="*60)
    print("性能对比汇总")
    print("="*60)
    
    comparison = performance_results["comparison"]
    print(f"查询数量: {comparison['num_queries']}")
    print(f"ChromaDB平均时间: {comparison['avg_chroma_time']:.4f}秒")
    print(f"ANN平均时间: {comparison['avg_ann_time']:.4f}秒")
    print(f"总体加速比: {comparison['overall_speedup']:.2f}x")
    print(f"总时间节省: {comparison['total_chroma_time'] - comparison['total_ann_time']:.4f}秒")
    
    # 计算平均重叠度
    overlaps = [result["overlap"] for result in performance_results["ann_results"]]
    avg_overlap = sum(overlaps) / len(overlaps) if overlaps else 0
    print(f"平均结果重叠度: {avg_overlap:.2f}")
    
    # 保存详细结果
    with open("integrated_ann_test_results.json", 'w', encoding='utf-8') as f:
        json.dump(performance_results, f, ensure_ascii=False, indent=2)
    
    print(f"\n详细结果已保存到: integrated_ann_test_results.json")
    
    # 性能建议
    print("\n" + "="*60)
    print("性能建议")
    print("="*60)
    
    if comparison['overall_speedup'] > 2:
        print("🚀 ANN算法显著提升了搜索性能！")
        print("   建议在生产环境中启用ANN搜索。")
    elif comparison['overall_speedup'] > 1.2:
        print("✅ ANN算法提供了适度的性能提升。")
        print("   在大规模数据集上效果会更明显。")
    else:
        print("⚠️  在当前数据规模下，ANN优势不明显。")
        print("   建议在更大的数据集上测试。")
    
    if avg_overlap > 0.8:
        print(f"✅ 结果质量良好，重叠度达到 {avg_overlap:.1%}")
    else:
        print(f"⚠️  结果重叠度较低 ({avg_overlap:.1%})，可能需要调优参数")


if __name__ == "__main__":
    main()
