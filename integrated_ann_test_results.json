{"chroma_results": [{"query": "机器学习算法的应用", "time": 0.005072832107543945, "documents": ["无监督学习是机器学习的另一种类型，其中算法从未标记的数据中发现隐藏的模式或结构。聚类和降维是无监督学习的常见应用。", "监督学习是机器学习的一种类型，其中算法从标记的训练数据中学习。目标是学习一个函数，该函数可以将输入映射到正确的输出。", "强化学习是机器学习的一种方法，其中智能体通过与环境交互来学习如何做出决策。智能体通过试错来学习，并根据获得的奖励或惩罚来调整其行为。", "机器学习是人工智能的一个重要子集，它使计算机能够在没有明确编程的情况下学习和改进。机器学习算法通过分析大量数据来识别模式，并使用这些模式来做出预测或决策。", "量子计算是利用量子力学现象进行计算的技术。量子计算机有潜力解决传统计算机无法处理的复杂问题。"]}, {"query": "编程语言的特点", "time": 0.0012159347534179688, "documents": ["云计算是通过互联网提供计算服务的模式，包括服务器、存储、数据库、网络、软件等。云计算为AI和机器学习提供了强大的计算资源。", "神经网络是受人脑神经元网络启发的计算模型。它们由相互连接的节点（神经元）组成，能够学习复杂的模式和关系。", "边缘计算是在数据源附近进行数据处理的计算模式。它减少了延迟，提高了效率，特别适合实时AI应用。", "Rust是一种系统编程语言，注重内存安全和性能。", "Go语言是Google开发的编程语言，以并发性能著称。"]}, {"query": "数据科学和分析", "time": 0.0012497901916503906, "documents": ["深度学习是机器学习的一个分支，使用多层神经网络来模拟人脑的工作方式。深度学习在图像识别、语音识别和自然语言处理等领域取得了突破性进展。", "无监督学习是机器学习的另一种类型，其中算法从未标记的数据中发现隐藏的模式或结构。聚类和降维是无监督学习的常见应用。", "监督学习是机器学习的一种类型，其中算法从标记的训练数据中学习。目标是学习一个函数，该函数可以将输入映射到正确的输出。", "机器学习是人工智能的一个重要子集，它使计算机能够在没有明确编程的情况下学习和改进。机器学习算法通过分析大量数据来识别模式，并使用这些模式来做出预测或决策。", "数据挖掘是从大型数据集中提取有用信息和知识的过程。它结合了统计学、机器学习和数据库技术来发现数据中的模式。"]}, {"query": "人工智能技术发展", "time": 0.0007309913635253906, "documents": ["人工智能基础知识", "自动驾驶汽车是人工智能在交通领域的重要应用。它们使用传感器、摄像头和AI算法来感知环境并做出驾驶决策。", "人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这些任务包括学习、推理、问题解决、感知和语言理解。", "计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。它涉及从数字图像或视频中提取有意义的信息。", "医疗AI是人工智能在医疗保健领域的应用，包括疾病诊断、药物发现、个性化治疗等。AI正在革命性地改变医疗行业。"]}, {"query": "Web开发技术栈", "time": 0.0006740093231201172, "documents": ["JavaScript是Web前端开发的核心语言，也可用于后端开发。", "PHP是一种服务器端脚本语言，广泛用于Web开发。", "Python是一种高级编程语言，广泛应用于数据科学、机器学习和Web开发。", "物联网（IoT）是指通过互联网连接的物理设备网络。这些设备可以收集和交换数据，为AI系统提供丰富的数据源。", "Go语言是Google开发的编程语言，以并发性能著称。"]}], "ann_results": [{"query": "机器学习算法的应用", "time": 0.004239082336425781, "documents": ["无监督学习是机器学习的另一种类型，其中算法从未标记的数据中发现隐藏的模式或结构。聚类和降维是无监督学习的常见应用。", "监督学习是机器学习的一种类型，其中算法从标记的训练数据中学习。目标是学习一个函数，该函数可以将输入映射到正确的输出。", "强化学习是机器学习的一种方法，其中智能体通过与环境交互来学习如何做出决策。智能体通过试错来学习，并根据获得的奖励或惩罚来调整其行为。", "机器学习是人工智能的一个重要子集，它使计算机能够在没有明确编程的情况下学习和改进。机器学习算法通过分析大量数据来识别模式，并使用这些模式来做出预测或决策。", "量子计算是利用量子力学现象进行计算的技术。量子计算机有潜力解决传统计算机无法处理的复杂问题。"], "overlap": 1.0}, {"query": "编程语言的特点", "time": 0.005023956298828125, "documents": ["云计算是通过互联网提供计算服务的模式，包括服务器、存储、数据库、网络、软件等。云计算为AI和机器学习提供了强大的计算资源。", "边缘计算是在数据源附近进行数据处理的计算模式。它减少了延迟，提高了效率，特别适合实时AI应用。", "Rust是一种系统编程语言，注重内存安全和性能。", "神经网络是受人脑神经元网络启发的计算模型。它们由相互连接的节点（神经元）组成，能够学习复杂的模式和关系。", "Go语言是Google开发的编程语言，以并发性能著称。"], "overlap": 1.0}, {"query": "数据科学和分析", "time": 0.005141019821166992, "documents": ["深度学习是机器学习的一个分支，使用多层神经网络来模拟人脑的工作方式。深度学习在图像识别、语音识别和自然语言处理等领域取得了突破性进展。", "无监督学习是机器学习的另一种类型，其中算法从未标记的数据中发现隐藏的模式或结构。聚类和降维是无监督学习的常见应用。", "监督学习是机器学习的一种类型，其中算法从标记的训练数据中学习。目标是学习一个函数，该函数可以将输入映射到正确的输出。", "机器学习是人工智能的一个重要子集，它使计算机能够在没有明确编程的情况下学习和改进。机器学习算法通过分析大量数据来识别模式，并使用这些模式来做出预测或决策。", "数据挖掘是从大型数据集中提取有用信息和知识的过程。它结合了统计学、机器学习和数据库技术来发现数据中的模式。"], "overlap": 1.0}, {"query": "人工智能技术发展", "time": 0.002279996871948242, "documents": ["人工智能基础知识", "自动驾驶汽车是人工智能在交通领域的重要应用。它们使用传感器、摄像头和AI算法来感知环境并做出驾驶决策。", "人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。这些任务包括学习、推理、问题解决、感知和语言理解。", "计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。它涉及从数字图像或视频中提取有意义的信息。", "医疗AI是人工智能在医疗保健领域的应用，包括疾病诊断、药物发现、个性化治疗等。AI正在革命性地改变医疗行业。"], "overlap": 1.0}, {"query": "Web开发技术栈", "time": 0.0020208358764648438, "documents": ["JavaScript是Web前端开发的核心语言，也可用于后端开发。", "PHP是一种服务器端脚本语言，广泛用于Web开发。", "Python是一种高级编程语言，广泛应用于数据科学、机器学习和Web开发。", "物联网（IoT）是指通过互联网连接的物理设备网络。这些设备可以收集和交换数据，为AI系统提供丰富的数据源。", "Go语言是Google开发的编程语言，以并发性能著称。"], "overlap": 1.0}], "comparison": {"total_chroma_time": 0.008943557739257812, "total_ann_time": 0.018704891204833984, "avg_chroma_time": 0.0017887115478515624, "avg_ann_time": 0.0037409782409667967, "overall_speedup": 0.4781400565936727, "num_queries": 5}}