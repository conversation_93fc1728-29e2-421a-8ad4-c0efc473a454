"""
召回测试模块
用于测试embedding模型的召回效果
"""
import time
import json
from typing import List, Dict, Any, Tuple
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from vector_store import VectorStore
from embedding_service import cosine_similarity
from config import TOP_K_RESULTS, SIMILARITY_THRESHOLD


class RetrievalTester:
    """召回测试器"""
    
    def __init__(self, vector_store: VectorStore):
        self.vector_store = vector_store
        self.test_results = []
    
    def evaluate_retrieval(self, 
                          queries: List[str], 
                          expected_docs: List[List[str]],
                          k: int = TOP_K_RESULTS) -> Dict[str, float]:
        """
        评估召回效果
        
        Args:
            queries: 查询列表
            expected_docs: 每个查询对应的期望文档列表
            k: 返回top-k结果
            
        Returns:
            评估指标字典
        """
        if len(queries) != len(expected_docs):
            raise ValueError("查询数量与期望文档数量不匹配")
        
        total_precision = 0
        total_recall = 0
        total_f1 = 0
        total_mrr = 0  # Mean Reciprocal Rank
        
        self.test_results = []
        
        for i, (query, expected) in enumerate(zip(queries, expected_docs)):
            print(f"测试查询 {i+1}/{len(queries)}: {query}")
            
            # 执行搜索
            start_time = time.time()
            results = self.vector_store.search(query, n_results=k)
            search_time = time.time() - start_time
            
            retrieved_docs = results["documents"][0] if results["documents"] else []
            distances = results["distances"][0] if results["distances"] else []
            
            # 计算精确率和召回率
            relevant_retrieved = []
            for doc in retrieved_docs:
                if any(exp in doc or doc in exp for exp in expected):
                    relevant_retrieved.append(doc)
            
            precision = len(relevant_retrieved) / len(retrieved_docs) if retrieved_docs else 0
            recall = len(relevant_retrieved) / len(expected) if expected else 0
            f1 = 2 * precision * recall / (precision + recall) if (precision + recall) > 0 else 0
            
            # 计算MRR
            mrr = 0
            for rank, doc in enumerate(retrieved_docs, 1):
                if any(exp in doc or doc in exp for exp in expected):
                    mrr = 1 / rank
                    break
            
            # 记录结果
            test_result = {
                "query": query,
                "expected_docs": expected,
                "retrieved_docs": retrieved_docs,
                "distances": distances,
                "precision": precision,
                "recall": recall,
                "f1": f1,
                "mrr": mrr,
                "search_time": search_time
            }
            self.test_results.append(test_result)
            
            total_precision += precision
            total_recall += recall
            total_f1 += f1
            total_mrr += mrr
            
            print(f"  精确率: {precision:.4f}, 召回率: {recall:.4f}, F1: {f1:.4f}")
        
        # 计算平均指标
        num_queries = len(queries)
        avg_metrics = {
            "avg_precision": total_precision / num_queries,
            "avg_recall": total_recall / num_queries,
            "avg_f1": total_f1 / num_queries,
            "avg_mrr": total_mrr / num_queries,
            "num_queries": num_queries
        }
        
        return avg_metrics
    
    def similarity_distribution_test(self, queries: List[str]) -> Dict[str, Any]:
        """
        测试相似度分布
        
        Args:
            queries: 查询列表
            
        Returns:
            相似度分布统计
        """
        all_similarities = []
        query_stats = []
        
        for query in queries:
            results = self.vector_store.search(query, n_results=10)
            distances = results["distances"][0] if results["distances"] else []
            
            # 将距离转换为相似度 (1 - normalized_distance)
            similarities = [1 - (d / max(distances)) if distances and max(distances) > 0 else 0 
                          for d in distances]
            
            all_similarities.extend(similarities)
            
            query_stat = {
                "query": query,
                "max_similarity": max(similarities) if similarities else 0,
                "min_similarity": min(similarities) if similarities else 0,
                "avg_similarity": sum(similarities) / len(similarities) if similarities else 0,
                "num_results": len(similarities)
            }
            query_stats.append(query_stat)
        
        return {
            "all_similarities": all_similarities,
            "query_stats": query_stats,
            "overall_avg": sum(all_similarities) / len(all_similarities) if all_similarities else 0,
            "overall_max": max(all_similarities) if all_similarities else 0,
            "overall_min": min(all_similarities) if all_similarities else 0
        }
    
    def performance_test(self, queries: List[str], batch_sizes: List[int] = [1, 5, 10]) -> Dict[str, Any]:
        """
        性能测试
        
        Args:
            queries: 查询列表
            batch_sizes: 批次大小列表
            
        Returns:
            性能测试结果
        """
        performance_results = {}
        
        for batch_size in batch_sizes:
            print(f"测试批次大小: {batch_size}")
            
            total_time = 0
            num_batches = 0
            
            for i in range(0, len(queries), batch_size):
                batch_queries = queries[i:i+batch_size]
                
                start_time = time.time()
                for query in batch_queries:
                    self.vector_store.search(query, n_results=5)
                batch_time = time.time() - start_time
                
                total_time += batch_time
                num_batches += 1
            
            avg_time_per_batch = total_time / num_batches if num_batches > 0 else 0
            avg_time_per_query = total_time / len(queries) if queries else 0
            
            performance_results[f"batch_size_{batch_size}"] = {
                "total_time": total_time,
                "avg_time_per_batch": avg_time_per_batch,
                "avg_time_per_query": avg_time_per_query,
                "queries_per_second": 1 / avg_time_per_query if avg_time_per_query > 0 else 0
            }
        
        return performance_results
    
    def generate_report(self, output_file: str = "retrieval_test_report.json"):
        """
        生成测试报告
        
        Args:
            output_file: 输出文件名
        """
        if not self.test_results:
            print("没有测试结果可生成报告")
            return
        
        report = {
            "test_summary": {
                "total_queries": len(self.test_results),
                "avg_precision": sum(r["precision"] for r in self.test_results) / len(self.test_results),
                "avg_recall": sum(r["recall"] for r in self.test_results) / len(self.test_results),
                "avg_f1": sum(r["f1"] for r in self.test_results) / len(self.test_results),
                "avg_mrr": sum(r["mrr"] for r in self.test_results) / len(self.test_results),
                "avg_search_time": sum(r["search_time"] for r in self.test_results) / len(self.test_results)
            },
            "detailed_results": self.test_results
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        print(f"测试报告已保存到: {output_file}")
    
    def plot_results(self):
        """绘制测试结果图表"""
        if not self.test_results:
            print("没有测试结果可绘制")
            return
        
        # 创建数据框
        df = pd.DataFrame(self.test_results)
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8')
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        
        # 精确率、召回率、F1分布
        metrics = ['precision', 'recall', 'f1']
        axes[0, 0].boxplot([df[metric] for metric in metrics], labels=metrics)
        axes[0, 0].set_title('精确率、召回率、F1分布')
        axes[0, 0].set_ylabel('分数')
        
        # 搜索时间分布
        axes[0, 1].hist(df['search_time'], bins=10, alpha=0.7)
        axes[0, 1].set_title('搜索时间分布')
        axes[0, 1].set_xlabel('时间 (秒)')
        axes[0, 1].set_ylabel('频次')
        
        # MRR分布
        axes[1, 0].bar(range(len(df)), df['mrr'])
        axes[1, 0].set_title('Mean Reciprocal Rank (MRR)')
        axes[1, 0].set_xlabel('查询索引')
        axes[1, 0].set_ylabel('MRR')
        
        # F1 vs 搜索时间散点图
        axes[1, 1].scatter(df['search_time'], df['f1'], alpha=0.7)
        axes[1, 1].set_title('F1分数 vs 搜索时间')
        axes[1, 1].set_xlabel('搜索时间 (秒)')
        axes[1, 1].set_ylabel('F1分数')
        
        plt.tight_layout()
        plt.savefig('retrieval_test_results.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("图表已保存为: retrieval_test_results.png")


if __name__ == "__main__":
    # 初始化向量存储
    vector_store = VectorStore()
    tester = RetrievalTester(vector_store)
    
    # 测试查询和期望结果
    test_queries = [
        "什么是机器学习？",
        "人工智能的应用领域",
        "深度学习和神经网络",
        "自然语言处理技术",
        "计算机视觉的原理"
    ]
    
    expected_results = [
        ["机器学习是人工智能的一个子集"],
        ["人工智能（AI）是计算机科学的一个分支"],
        ["深度学习是机器学习的一个分支"],
        ["自然语言处理（NLP）是人工智能的一个领域"],
        ["计算机视觉是人工智能的一个分支"]
    ]
    
    print("开始召回测试...")
    
    # 评估召回效果
    metrics = tester.evaluate_retrieval(test_queries, expected_results)
    
    print("\n=== 测试结果 ===")
    print(f"平均精确率: {metrics['avg_precision']:.4f}")
    print(f"平均召回率: {metrics['avg_recall']:.4f}")
    print(f"平均F1分数: {metrics['avg_f1']:.4f}")
    print(f"平均MRR: {metrics['avg_mrr']:.4f}")
    
    # 相似度分布测试
    print("\n=== 相似度分布测试 ===")
    sim_stats = tester.similarity_distribution_test(test_queries)
    print(f"整体平均相似度: {sim_stats['overall_avg']:.4f}")
    print(f"最高相似度: {sim_stats['overall_max']:.4f}")
    print(f"最低相似度: {sim_stats['overall_min']:.4f}")
    
    # 性能测试
    print("\n=== 性能测试 ===")
    perf_results = tester.performance_test(test_queries)
    for batch_size, stats in perf_results.items():
        print(f"{batch_size}: {stats['queries_per_second']:.2f} 查询/秒")
    
    # 生成报告
    tester.generate_report()
    
    # 绘制结果图表
    try:
        tester.plot_results()
    except Exception as e:
        print(f"绘制图表时出错: {e}")
        print("可能需要安装图形后端，跳过图表生成")
