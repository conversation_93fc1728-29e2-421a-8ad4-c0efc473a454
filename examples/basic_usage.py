"""
基本使用示例
演示如何使用embedding模型进行文档索引和搜索
"""
import sys
import os

# 添加父目录到路径，以便导入模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from embedding_service import OllamaEmbeddingService, cosine_similarity
from vector_store import VectorStore


def example_1_basic_embedding():
    """示例1: 基本embedding生成和相似度计算"""
    print("=== 示例1: 基本Embedding操作 ===")
    
    # 初始化embedding服务
    service = OllamaEmbeddingService()
    
    # 测试文本
    texts = [
        "Python是一种编程语言",
        "Java是一种面向对象的编程语言", 
        "今天天气很好",
        "机器学习是人工智能的分支"
    ]
    
    print("生成embedding向量...")
    embeddings = service.get_embeddings_batch(texts)
    
    if embeddings:
        print(f"成功生成 {len(embeddings)} 个embedding向量")
        print(f"向量维度: {len(embeddings[0])}")
        
        # 计算相似度
        print("\n相似度矩阵:")
        for i, text1 in enumerate(texts):
            for j, text2 in enumerate(texts):
                if i <= j:
                    sim = cosine_similarity(embeddings[i], embeddings[j])
                    print(f"文本{i+1} vs 文本{j+1}: {sim:.4f}")
    else:
        print("生成embedding失败")


def example_2_document_indexing():
    """示例2: 文档索引和搜索"""
    print("\n=== 示例2: 文档索引和搜索 ===")
    
    # 初始化向量存储
    vector_store = VectorStore(collection_name="example_collection")
    
    # 示例文档
    documents = [
        "Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。",
        "机器学习是人工智能的一个分支，使计算机能够从数据中学习。",
        "深度学习使用神经网络来模拟人脑的工作方式。",
        "自然语言处理帮助计算机理解和生成人类语言。",
        "计算机视觉使机器能够理解和解释视觉信息。",
        "数据科学结合了统计学、编程和领域知识来从数据中提取洞察。"
    ]
    
    # 清空集合（如果需要重新开始）
    vector_store.clear_collection()
    
    # 添加文档
    print("添加文档到向量数据库...")
    doc_ids = vector_store.add_documents(documents)
    print(f"成功添加 {len(doc_ids)} 个文档")
    
    # 搜索测试
    queries = [
        "编程语言",
        "人工智能技术",
        "数据分析"
    ]
    
    print("\n搜索测试:")
    for query in queries:
        print(f"\n查询: {query}")
        results = vector_store.search(query, n_results=3)
        
        if results["documents"] and results["documents"][0]:
            for i, (doc, distance) in enumerate(zip(results["documents"][0], results["distances"][0])):
                print(f"  {i+1}. 距离: {distance:.4f}")
                print(f"     文档: {doc}")
        else:
            print("  未找到结果")


def example_3_similarity_search():
    """示例3: 相似性搜索和阈值过滤"""
    print("\n=== 示例3: 相似性搜索和阈值过滤 ===")
    
    # 使用现有的向量存储
    vector_store = VectorStore(collection_name="example_collection")
    
    query = "机器学习算法"
    print(f"查询: {query}")
    
    # 获取更多结果
    results = vector_store.search(query, n_results=10)
    
    if results["documents"] and results["documents"][0]:
        print("所有搜索结果:")
        for i, (doc, distance) in enumerate(zip(results["documents"][0], results["distances"][0])):
            # 将距离转换为相似度分数
            similarity = 1 / (1 + distance)
            print(f"  {i+1}. 相似度: {similarity:.4f} | 文档: {doc[:80]}...")
        
        # 应用相似度阈值
        threshold = 0.3
        print(f"\n应用相似度阈值 {threshold}:")
        filtered_results = []
        for doc, distance in zip(results["documents"][0], results["distances"][0]):
            similarity = 1 / (1 + distance)
            if similarity >= threshold:
                filtered_results.append((doc, similarity))
        
        if filtered_results:
            for i, (doc, sim) in enumerate(filtered_results):
                print(f"  {i+1}. 相似度: {sim:.4f} | 文档: {doc[:80]}...")
        else:
            print("  没有文档满足相似度阈值")
    else:
        print("未找到结果")


def example_4_batch_processing():
    """示例4: 批量处理和性能测试"""
    print("\n=== 示例4: 批量处理和性能测试 ===")
    
    import time
    
    # 初始化服务
    service = OllamaEmbeddingService()
    
    # 批量文本
    batch_texts = [
        f"这是第{i}个测试文档，内容关于人工智能和机器学习的应用。"
        for i in range(1, 11)
    ]
    
    # 测试批量处理性能
    print("测试批量embedding生成性能...")
    
    start_time = time.time()
    embeddings = service.get_embeddings_batch(batch_texts)
    end_time = time.time()
    
    if embeddings:
        print(f"处理 {len(batch_texts)} 个文本")
        print(f"总时间: {end_time - start_time:.2f} 秒")
        print(f"平均每个文本: {(end_time - start_time) / len(batch_texts):.3f} 秒")
        print(f"处理速度: {len(batch_texts) / (end_time - start_time):.2f} 文本/秒")
    else:
        print("批量处理失败")


def example_5_custom_metadata():
    """示例5: 使用自定义元数据"""
    print("\n=== 示例5: 使用自定义元数据 ===")
    
    # 初始化向量存储
    vector_store = VectorStore(collection_name="metadata_example")
    vector_store.clear_collection()
    
    # 带元数据的文档
    documents = [
        "Python编程语言简介",
        "Java面向对象编程",
        "JavaScript前端开发",
        "机器学习基础概念",
        "深度学习神经网络"
    ]
    
    metadatas = [
        {"category": "编程语言", "difficulty": "初级", "language": "Python"},
        {"category": "编程语言", "difficulty": "中级", "language": "Java"},
        {"category": "编程语言", "difficulty": "初级", "language": "JavaScript"},
        {"category": "人工智能", "difficulty": "中级", "topic": "机器学习"},
        {"category": "人工智能", "difficulty": "高级", "topic": "深度学习"}
    ]
    
    # 添加带元数据的文档
    print("添加带元数据的文档...")
    doc_ids = vector_store.add_documents(documents, metadatas=metadatas)
    
    # 使用元数据过滤搜索
    print("\n使用元数据过滤搜索:")
    
    # 只搜索编程语言相关的文档
    query = "编程"
    where_condition = {"category": "编程语言"}
    
    results = vector_store.search(query, n_results=5, where=where_condition)
    
    print(f"查询: {query} (过滤条件: category='编程语言')")
    if results["documents"] and results["documents"][0]:
        for i, (doc, metadata, distance) in enumerate(zip(
            results["documents"][0], 
            results["metadatas"][0], 
            results["distances"][0]
        )):
            print(f"  {i+1}. 文档: {doc}")
            print(f"     元数据: {metadata}")
            print(f"     距离: {distance:.4f}")
    else:
        print("未找到结果")


def main():
    """运行所有示例"""
    print("Embedding模型基本使用示例")
    print("=" * 50)
    
    try:
        example_1_basic_embedding()
        example_2_document_indexing()
        example_3_similarity_search()
        example_4_batch_processing()
        example_5_custom_metadata()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成！")
        
    except Exception as e:
        print(f"运行示例时出错: {e}")
        print("请确保Ollama服务正在运行，并且已安装所有依赖")


if __name__ == "__main__":
    main()
