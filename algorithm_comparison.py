"""
算法对比测试框架
比较量化ANN算法与线性搜索的性能和准确性
"""
import time
import json
import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
from typing import List, Dict, Any, Tuple
from quantized_ann import QuantizedANNIndex, LinearSearchIndex, VectorQuantizer, create_test_data
from embedding_service import OllamaEmbeddingService


class AlgorithmComparator:
    """算法对比器"""
    
    def __init__(self, embedding_service: OllamaEmbeddingService):
        self.embedding_service = embedding_service
        self.results = []
        
    def run_comprehensive_comparison(self, 
                                   data_sizes: List[int] = [50, 100, 200, 500],
                                   query_counts: List[int] = [10, 20, 50],
                                   k_values: List[int] = [5, 10, 20]) -> Dict[str, Any]:
        """
        运行全面的对比测试
        
        Args:
            data_sizes: 测试数据大小列表
            query_counts: 查询数量列表  
            k_values: top-k值列表
            
        Returns:
            测试结果字典
        """
        print("=== 开始全面算法对比测试 ===")
        
        all_results = {
            "test_config": {
                "data_sizes": data_sizes,
                "query_counts": query_counts,
                "k_values": k_values
            },
            "detailed_results": [],
            "summary": {}
        }
        
        for data_size in data_sizes:
            print(f"\n测试数据大小: {data_size}")
            
            # 创建测试数据
            vectors, documents = create_test_data(self.embedding_service, num_docs=data_size)
            
            if not vectors:
                print(f"跳过数据大小 {data_size} - 生成数据失败")
                continue
            
            # 设置索引
            ann_index, linear_index = self._setup_indices(vectors)
            
            for query_count in query_counts:
                for k in k_values:
                    print(f"  查询数量: {query_count}, k: {k}")
                    
                    result = self._run_single_test(
                        ann_index, linear_index, documents, 
                        data_size, query_count, k
                    )
                    
                    all_results["detailed_results"].append(result)
        
        # 生成汇总统计
        all_results["summary"] = self._generate_summary(all_results["detailed_results"])
        
        return all_results
    
    def _setup_indices(self, vectors: List[np.ndarray]) -> Tuple[QuantizedANNIndex, LinearSearchIndex]:
        """设置索引"""
        # 训练量化器
        quantizer = VectorQuantizer(
            n_clusters=min(64, len(vectors) // 4),  # 动态调整聚类数
            n_bits=8
        )
        quantizer.fit(np.array(vectors))
        
        # 创建索引
        ann_index = QuantizedANNIndex(quantizer, top_k_candidates=min(50, len(vectors) // 2))
        linear_index = LinearSearchIndex()
        
        # 添加向量
        ann_index.add_vectors(vectors)
        linear_index.add_vectors(vectors)
        
        return ann_index, linear_index
    
    def _run_single_test(self, 
                        ann_index: QuantizedANNIndex,
                        linear_index: LinearSearchIndex,
                        documents: List[str],
                        data_size: int,
                        query_count: int,
                        k: int) -> Dict[str, Any]:
        """运行单个测试"""
        
        # 生成测试查询
        test_queries = self._generate_test_queries(query_count)
        
        ann_times = []
        linear_times = []
        overlaps = []
        precision_scores = []
        
        for query_text in test_queries:
            # 生成查询向量
            query_embedding = self.embedding_service.get_embedding(query_text)
            if not query_embedding:
                continue
            
            query_vector = np.array(query_embedding)
            
            # ANN搜索
            ann_indices, ann_scores, ann_time = ann_index.search(query_vector, k=k)
            ann_times.append(ann_time)
            
            # 线性搜索
            linear_indices, linear_scores, linear_time = linear_index.search(query_vector, k=k)
            linear_times.append(linear_time)
            
            # 计算重叠度
            overlap = len(set(ann_indices) & set(linear_indices))
            overlaps.append(overlap / k)
            
            # 计算精确度（ANN结果在线性搜索top-k中的比例）
            precision = len(set(ann_indices) & set(linear_indices[:k])) / len(ann_indices)
            precision_scores.append(precision)
        
        # 计算统计指标
        result = {
            "data_size": data_size,
            "query_count": query_count,
            "k": k,
            "ann_avg_time": np.mean(ann_times),
            "linear_avg_time": np.mean(linear_times),
            "speedup": np.mean(linear_times) / np.mean(ann_times) if ann_times else 0,
            "avg_overlap": np.mean(overlaps),
            "avg_precision": np.mean(precision_scores),
            "ann_std_time": np.std(ann_times),
            "linear_std_time": np.std(linear_times),
            "total_ann_time": sum(ann_times),
            "total_linear_time": sum(linear_times)
        }
        
        return result
    
    def _generate_test_queries(self, count: int) -> List[str]:
        """生成测试查询"""
        base_queries = [
            "机器学习算法的实现",
            "深度学习神经网络架构",
            "自然语言处理技术应用",
            "计算机视觉图像识别",
            "数据科学分析方法",
            "云计算分布式架构",
            "网络安全防护策略",
            "移动应用开发框架",
            "前端用户界面设计",
            "数据库性能优化技术",
            "人工智能伦理问题",
            "区块链技术应用",
            "物联网设备连接",
            "大数据处理平台",
            "软件工程最佳实践"
        ]
        
        # 循环使用查询，确保有足够的查询数量
        queries = []
        for i in range(count):
            queries.append(base_queries[i % len(base_queries)])
        
        return queries
    
    def _generate_summary(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """生成汇总统计"""
        if not results:
            return {}
        
        df = pd.DataFrame(results)
        
        summary = {
            "overall_stats": {
                "avg_speedup": df['speedup'].mean(),
                "max_speedup": df['speedup'].max(),
                "min_speedup": df['speedup'].min(),
                "avg_precision": df['avg_precision'].mean(),
                "avg_overlap": df['avg_overlap'].mean()
            },
            "by_data_size": {},
            "by_k_value": {}
        }
        
        # 按数据大小分组
        for data_size in df['data_size'].unique():
            subset = df[df['data_size'] == data_size]
            summary["by_data_size"][str(data_size)] = {
                "avg_speedup": subset['speedup'].mean(),
                "avg_precision": subset['avg_precision'].mean(),
                "avg_ann_time": subset['ann_avg_time'].mean(),
                "avg_linear_time": subset['linear_avg_time'].mean()
            }
        
        # 按k值分组
        for k in df['k'].unique():
            subset = df[df['k'] == k]
            summary["by_k_value"][str(k)] = {
                "avg_speedup": subset['speedup'].mean(),
                "avg_precision": subset['avg_precision'].mean(),
                "avg_overlap": subset['avg_overlap'].mean()
            }
        
        return summary
    
    def save_results(self, results: Dict[str, Any], filename: str = "algorithm_comparison_results.json"):
        """保存结果到文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        print(f"结果已保存到: {filename}")
    
    def plot_results(self, results: Dict[str, Any]):
        """绘制结果图表"""
        df = pd.DataFrame(results["detailed_results"])

        if df.empty:
            print("没有数据可绘制")
            return

        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 加速比 vs 数据大小
        data_size_stats = df.groupby('data_size').agg({
            'speedup': 'mean',
            'avg_precision': 'mean'
        }).reset_index()

        axes[0, 0].plot(data_size_stats['data_size'], data_size_stats['speedup'], 'bo-')
        axes[0, 0].set_xlabel('Data Size')
        axes[0, 0].set_ylabel('Average Speedup')
        axes[0, 0].set_title('Speedup vs Data Size')
        axes[0, 0].grid(True)

        # 2. 精确度 vs 数据大小
        axes[0, 1].plot(data_size_stats['data_size'], data_size_stats['avg_precision'], 'ro-')
        axes[0, 1].set_xlabel('Data Size')
        axes[0, 1].set_ylabel('Average Precision')
        axes[0, 1].set_title('Precision vs Data Size')
        axes[0, 1].grid(True)
        
        # 3. 搜索时间对比
        time_comparison = df.groupby('data_size').agg({
            'ann_avg_time': 'mean',
            'linear_avg_time': 'mean'
        }).reset_index()

        x = range(len(time_comparison))
        width = 0.35

        axes[1, 0].bar([i - width/2 for i in x], time_comparison['ann_avg_time'],
                      width, label='ANN Search', alpha=0.8)
        axes[1, 0].bar([i + width/2 for i in x], time_comparison['linear_avg_time'],
                      width, label='Linear Search', alpha=0.8)

        axes[1, 0].set_xlabel('Data Size')
        axes[1, 0].set_ylabel('Average Search Time (seconds)')
        axes[1, 0].set_title('Search Time Comparison')
        axes[1, 0].set_xticks(x)
        axes[1, 0].set_xticklabels(time_comparison['data_size'])
        axes[1, 0].legend()
        axes[1, 0].grid(True)
        
        # 4. k值对性能的影响
        k_stats = df.groupby('k').agg({
            'speedup': 'mean',
            'avg_precision': 'mean'
        }).reset_index()

        ax2 = axes[1, 1]
        ax3 = ax2.twinx()

        line1 = ax2.plot(k_stats['k'], k_stats['speedup'], 'b-o', label='Speedup')
        line2 = ax3.plot(k_stats['k'], k_stats['avg_precision'], 'r-s', label='Precision')

        ax2.set_xlabel('k Value')
        ax2.set_ylabel('Average Speedup', color='b')
        ax3.set_ylabel('Average Precision', color='r')
        ax2.set_title('Performance vs k Value')

        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax2.legend(lines, labels, loc='upper left')
        
        plt.tight_layout()
        plt.savefig('algorithm_comparison_plots.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        print("图表已保存为: algorithm_comparison_plots.png")
    
    def print_summary(self, results: Dict[str, Any]):
        """打印汇总结果"""
        summary = results.get("summary", {})
        overall = summary.get("overall_stats", {})
        
        print("\n" + "="*60)
        print("算法对比测试汇总结果")
        print("="*60)
        
        print(f"平均加速比: {overall.get('avg_speedup', 0):.2f}x")
        print(f"最大加速比: {overall.get('max_speedup', 0):.2f}x")
        print(f"最小加速比: {overall.get('min_speedup', 0):.2f}x")
        print(f"平均精确度: {overall.get('avg_precision', 0):.3f}")
        print(f"平均重叠度: {overall.get('avg_overlap', 0):.3f}")
        
        print("\n按数据大小分组:")
        for size, stats in summary.get("by_data_size", {}).items():
            print(f"  数据大小 {size}:")
            print(f"    加速比: {stats.get('avg_speedup', 0):.2f}x")
            print(f"    精确度: {stats.get('avg_precision', 0):.3f}")
            print(f"    ANN平均时间: {stats.get('avg_ann_time', 0):.4f}秒")
            print(f"    线性平均时间: {stats.get('avg_linear_time', 0):.4f}秒")
        
        print("\n按k值分组:")
        for k, stats in summary.get("by_k_value", {}).items():
            print(f"  k={k}:")
            print(f"    加速比: {stats.get('avg_speedup', 0):.2f}x")
            print(f"    精确度: {stats.get('avg_precision', 0):.3f}")
            print(f"    重叠度: {stats.get('avg_overlap', 0):.3f}")


if __name__ == "__main__":
    print("=== 算法对比测试框架 ===")
    
    # 初始化
    embedding_service = OllamaEmbeddingService()
    
    if not embedding_service.test_connection():
        print("❌ Ollama连接失败")
        exit(1)
    
    comparator = AlgorithmComparator(embedding_service)
    
    # 运行对比测试
    print("开始运行对比测试...")
    results = comparator.run_comprehensive_comparison(
        data_sizes=[50, 100, 200],  # 减少测试规模以节省时间
        query_counts=[5, 10],
        k_values=[5, 10]
    )
    
    # 输出结果
    comparator.print_summary(results)
    
    # 保存结果
    comparator.save_results(results)
    
    # 绘制图表
    try:
        comparator.plot_results(results)
    except Exception as e:
        print(f"绘制图表时出错: {e}")
        print("跳过图表生成")
