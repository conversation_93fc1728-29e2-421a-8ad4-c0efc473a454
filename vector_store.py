"""
向量数据库操作模块
使用ChromaDB作为向量数据库
"""
import chromadb
from chromadb.config import Settings
import uuid
from typing import List, Dict, Any, Optional, Tuple
from embedding_service import OllamaEmbeddingService
from config import CHROMA_PERSIST_DIRECTORY, COLLECTION_NAME


class VectorStore:
    """向量数据库操作类"""
    
    def __init__(self, 
                 persist_directory: str = CHROMA_PERSIST_DIRECTORY,
                 collection_name: str = COLLECTION_NAME):
        """
        初始化向量数据库
        
        Args:
            persist_directory: 数据库持久化目录
            collection_name: 集合名称
        """
        self.persist_directory = persist_directory
        self.collection_name = collection_name
        self.embedding_service = OllamaEmbeddingService()
        
        # 初始化ChromaDB客户端
        self.client = chromadb.PersistentClient(path=persist_directory)
        
        # 获取或创建集合
        try:
            self.collection = self.client.get_collection(name=collection_name)
            print(f"加载现有集合: {collection_name}")
        except Exception:
            self.collection = self.client.create_collection(name=collection_name)
            print(f"创建新集合: {collection_name}")
    
    def add_documents(self, 
                     documents: List[str], 
                     metadatas: Optional[List[Dict[str, Any]]] = None,
                     ids: Optional[List[str]] = None) -> List[str]:
        """
        添加文档到向量数据库
        
        Args:
            documents: 文档文本列表
            metadatas: 文档元数据列表
            ids: 文档ID列表，如果不提供则自动生成
            
        Returns:
            文档ID列表
        """
        if not documents:
            return []
        
        # 生成embedding向量
        print(f"正在为 {len(documents)} 个文档生成embedding...")
        embeddings = self.embedding_service.get_embeddings_batch(documents)
        
        if len(embeddings) != len(documents):
            print(f"警告: 只成功生成了 {len(embeddings)}/{len(documents)} 个embedding")
            # 只处理成功生成embedding的文档
            documents = documents[:len(embeddings)]
            if metadatas:
                metadatas = metadatas[:len(embeddings)]
        
        # 生成ID
        if ids is None:
            ids = [str(uuid.uuid4()) for _ in range(len(documents))]
        
        # 准备元数据
        if metadatas is None:
            metadatas = [{"text": doc} for doc in documents]
        
        # 添加到数据库
        self.collection.add(
            embeddings=embeddings,
            documents=documents,
            metadatas=metadatas,
            ids=ids
        )
        
        print(f"成功添加 {len(documents)} 个文档到向量数据库")
        return ids
    
    def search(self, 
               query: str, 
               n_results: int = 5,
               where: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        搜索相似文档
        
        Args:
            query: 查询文本
            n_results: 返回结果数量
            where: 过滤条件
            
        Returns:
            搜索结果字典
        """
        # 生成查询embedding
        query_embedding = self.embedding_service.get_embedding(query)
        
        if not query_embedding:
            print("生成查询embedding失败")
            return {"documents": [], "metadatas": [], "distances": [], "ids": []}
        
        # 执行搜索
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=n_results,
            where=where
        )
        
        return results
    
    def get_collection_info(self) -> Dict[str, Any]:
        """
        获取集合信息
        
        Returns:
            集合信息字典
        """
        count = self.collection.count()
        return {
            "name": self.collection_name,
            "count": count,
            "persist_directory": self.persist_directory
        }
    
    def delete_documents(self, ids: List[str]) -> bool:
        """
        删除文档
        
        Args:
            ids: 要删除的文档ID列表
            
        Returns:
            是否删除成功
        """
        try:
            self.collection.delete(ids=ids)
            print(f"成功删除 {len(ids)} 个文档")
            return True
        except Exception as e:
            print(f"删除文档失败: {e}")
            return False
    
    def clear_collection(self) -> bool:
        """
        清空集合
        
        Returns:
            是否清空成功
        """
        try:
            # 删除现有集合
            self.client.delete_collection(name=self.collection_name)
            # 重新创建集合
            self.collection = self.client.create_collection(name=self.collection_name)
            print(f"成功清空集合: {self.collection_name}")
            return True
        except Exception as e:
            print(f"清空集合失败: {e}")
            return False
    
    def get_all_documents(self) -> Dict[str, Any]:
        """
        获取所有文档
        
        Returns:
            所有文档的字典
        """
        try:
            results = self.collection.get()
            return results
        except Exception as e:
            print(f"获取所有文档失败: {e}")
            return {"documents": [], "metadatas": [], "ids": []}


def split_text_into_chunks(text: str, chunk_size: int = 500, overlap: int = 50) -> List[str]:
    """
    将长文本分割成块
    
    Args:
        text: 输入文本
        chunk_size: 块大小（字符数）
        overlap: 重叠字符数
        
    Returns:
        文本块列表
    """
    if len(text) <= chunk_size:
        return [text]
    
    chunks = []
    start = 0
    
    while start < len(text):
        end = start + chunk_size
        chunk = text[start:end]
        chunks.append(chunk)
        
        if end >= len(text):
            break
            
        start = end - overlap
    
    return chunks


if __name__ == "__main__":
    # 测试向量数据库
    vector_store = VectorStore()
    
    # 获取集合信息
    info = vector_store.get_collection_info()
    print(f"集合信息: {info}")
    
    # 测试文档
    test_docs = [
        "人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。",
        "机器学习是人工智能的一个子集，它使计算机能够在没有明确编程的情况下学习和改进。",
        "深度学习是机器学习的一个分支，使用神经网络来模拟人脑的工作方式。",
        "自然语言处理（NLP）是人工智能的一个领域，专注于计算机与人类语言之间的交互。",
        "计算机视觉是人工智能的一个分支，使计算机能够理解和解释视觉信息。"
    ]
    
    # 添加文档
    print("\n添加测试文档...")
    doc_ids = vector_store.add_documents(test_docs)
    
    # 更新集合信息
    info = vector_store.get_collection_info()
    print(f"更新后的集合信息: {info}")
    
    # 测试搜索
    print("\n测试搜索功能...")
    query = "什么是机器学习？"
    results = vector_store.search(query, n_results=3)
    
    print(f"查询: {query}")
    print("搜索结果:")
    for i, (doc, distance) in enumerate(zip(results["documents"][0], results["distances"][0])):
        print(f"{i+1}. 相似度: {1-distance:.4f}")
        print(f"   文档: {doc}")
        print()
