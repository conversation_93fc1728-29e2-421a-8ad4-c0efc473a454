"""
生成清晰的性能对比图表（无乱码版本）
"""
import json
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
from typing import Dict, Any


def load_results(filename: str = "algorithm_comparison_results.json") -> Dict[str, Any]:
    """加载测试结果"""
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"结果文件 {filename} 不存在")
        return {}


def create_performance_plots(results: Dict[str, Any]):
    """创建性能对比图表"""
    df = pd.DataFrame(results["detailed_results"])
    
    if df.empty:
        print("没有数据可绘制")
        return
    
    # 设置图表样式
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (16, 12)
    plt.rcParams['font.size'] = 12
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. Speedup vs Data Size
    data_size_stats = df.groupby('data_size').agg({
        'speedup': 'mean',
        'avg_precision': 'mean'
    }).reset_index()
    
    axes[0, 0].plot(data_size_stats['data_size'], data_size_stats['speedup'], 
                   'bo-', linewidth=2, markersize=8)
    axes[0, 0].set_xlabel('Data Size (Number of Documents)', fontsize=12)
    axes[0, 0].set_ylabel('Average Speedup (x)', fontsize=12)
    axes[0, 0].set_title('ANN vs Linear Search: Speedup Performance', fontsize=14, fontweight='bold')
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].set_ylim(bottom=0)
    
    # 添加数值标签
    for x, y in zip(data_size_stats['data_size'], data_size_stats['speedup']):
        axes[0, 0].annotate(f'{y:.2f}x', (x, y), textcoords="offset points", 
                           xytext=(0,10), ha='center', fontsize=10)
    
    # 2. Precision vs Data Size
    axes[0, 1].plot(data_size_stats['data_size'], data_size_stats['avg_precision'], 
                   'ro-', linewidth=2, markersize=8)
    axes[0, 1].set_xlabel('Data Size (Number of Documents)', fontsize=12)
    axes[0, 1].set_ylabel('Average Precision', fontsize=12)
    axes[0, 1].set_title('ANN Algorithm: Precision Retention', fontsize=14, fontweight='bold')
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].set_ylim(0, 1.1)
    
    # 添加数值标签
    for x, y in zip(data_size_stats['data_size'], data_size_stats['avg_precision']):
        axes[0, 1].annotate(f'{y:.3f}', (x, y), textcoords="offset points", 
                           xytext=(0,10), ha='center', fontsize=10)
    
    # 3. Search Time Comparison
    time_comparison = df.groupby('data_size').agg({
        'ann_avg_time': 'mean',
        'linear_avg_time': 'mean'
    }).reset_index()
    
    x = np.arange(len(time_comparison))
    width = 0.35
    
    bars1 = axes[1, 0].bar(x - width/2, time_comparison['ann_avg_time'], 
                          width, label='ANN Search', alpha=0.8, color='skyblue')
    bars2 = axes[1, 0].bar(x + width/2, time_comparison['linear_avg_time'], 
                          width, label='Linear Search', alpha=0.8, color='lightcoral')
    
    axes[1, 0].set_xlabel('Data Size (Number of Documents)', fontsize=12)
    axes[1, 0].set_ylabel('Average Search Time (seconds)', fontsize=12)
    axes[1, 0].set_title('Search Time Comparison', fontsize=14, fontweight='bold')
    axes[1, 0].set_xticks(x)
    axes[1, 0].set_xticklabels(time_comparison['data_size'])
    axes[1, 0].legend(fontsize=11)
    axes[1, 0].grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar in bars1:
        height = bar.get_height()
        axes[1, 0].annotate(f'{height:.4f}s', xy=(bar.get_x() + bar.get_width()/2, height),
                           xytext=(0, 3), textcoords="offset points", ha='center', va='bottom', fontsize=9)
    
    for bar in bars2:
        height = bar.get_height()
        axes[1, 0].annotate(f'{height:.4f}s', xy=(bar.get_x() + bar.get_width()/2, height),
                           xytext=(0, 3), textcoords="offset points", ha='center', va='bottom', fontsize=9)
    
    # 4. Performance vs k Value
    k_stats = df.groupby('k').agg({
        'speedup': 'mean',
        'avg_precision': 'mean'
    }).reset_index()
    
    ax2 = axes[1, 1]
    ax3 = ax2.twinx()
    
    line1 = ax2.plot(k_stats['k'], k_stats['speedup'], 'b-o', linewidth=2, 
                    markersize=8, label='Speedup')
    line2 = ax3.plot(k_stats['k'], k_stats['avg_precision'], 'r-s', linewidth=2, 
                    markersize=8, label='Precision')
    
    ax2.set_xlabel('k Value (Top-k Results)', fontsize=12)
    ax2.set_ylabel('Average Speedup (x)', color='b', fontsize=12)
    ax3.set_ylabel('Average Precision', color='r', fontsize=12)
    ax2.set_title('Performance Impact of k Value', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)
    
    # 设置y轴颜色
    ax2.tick_params(axis='y', labelcolor='b')
    ax3.tick_params(axis='y', labelcolor='r')
    
    # 合并图例
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax2.legend(lines, labels, loc='upper right', fontsize=11)
    
    plt.tight_layout(pad=3.0)
    plt.savefig('algorithm_comparison_clean.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("清晰图表已保存为: algorithm_comparison_clean.png")


def create_summary_plot(results: Dict[str, Any]):
    """创建汇总性能图表"""
    summary = results.get("summary", {})
    overall = summary.get("overall_stats", {})
    
    if not overall:
        print("没有汇总数据可绘制")
        return
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(14, 6))
    
    # 1. 整体性能指标
    metrics = ['Avg Speedup', 'Max Speedup', 'Min Speedup']
    values = [
        overall.get('avg_speedup', 0),
        overall.get('max_speedup', 0), 
        overall.get('min_speedup', 0)
    ]
    colors = ['skyblue', 'lightgreen', 'lightcoral']
    
    bars = ax1.bar(metrics, values, color=colors, alpha=0.8)
    ax1.set_ylabel('Speedup (x)', fontsize=12)
    ax1.set_title('Overall Speedup Performance', fontsize=14, fontweight='bold')
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars, values):
        ax1.annotate(f'{value:.2f}x', xy=(bar.get_x() + bar.get_width()/2, bar.get_height()),
                    xytext=(0, 3), textcoords="offset points", ha='center', va='bottom', fontsize=11)
    
    # 2. 质量指标
    quality_metrics = ['Precision', 'Overlap']
    quality_values = [
        overall.get('avg_precision', 0),
        overall.get('avg_overlap', 0)
    ]
    quality_colors = ['gold', 'lightblue']
    
    bars2 = ax2.bar(quality_metrics, quality_values, color=quality_colors, alpha=0.8)
    ax2.set_ylabel('Score', fontsize=12)
    ax2.set_title('Algorithm Quality Metrics', fontsize=14, fontweight='bold')
    ax2.set_ylim(0, 1.1)
    ax2.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars2, quality_values):
        ax2.annotate(f'{value:.3f}', xy=(bar.get_x() + bar.get_width()/2, bar.get_height()),
                    xytext=(0, 3), textcoords="offset points", ha='center', va='bottom', fontsize=11)
    
    plt.tight_layout()
    plt.savefig('algorithm_summary_clean.png', dpi=300, bbox_inches='tight')
    plt.show()
    
    print("汇总图表已保存为: algorithm_summary_clean.png")


def print_performance_summary(results: Dict[str, Any]):
    """打印性能汇总"""
    summary = results.get("summary", {})
    overall = summary.get("overall_stats", {})
    
    print("\n" + "="*60)
    print("ALGORITHM PERFORMANCE SUMMARY")
    print("="*60)
    print(f"Average Speedup:    {overall.get('avg_speedup', 0):.2f}x")
    print(f"Maximum Speedup:    {overall.get('max_speedup', 0):.2f}x")
    print(f"Minimum Speedup:    {overall.get('min_speedup', 0):.2f}x")
    print(f"Average Precision:  {overall.get('avg_precision', 0):.3f} ({overall.get('avg_precision', 0)*100:.1f}%)")
    print(f"Average Overlap:    {overall.get('avg_overlap', 0):.3f} ({overall.get('avg_overlap', 0)*100:.1f}%)")
    
    print(f"\nPERFORMANCE BY DATA SIZE:")
    for size, stats in summary.get("by_data_size", {}).items():
        print(f"  {size} documents:")
        print(f"    Speedup:     {stats.get('avg_speedup', 0):.2f}x")
        print(f"    Precision:   {stats.get('avg_precision', 0):.3f}")
        print(f"    ANN Time:    {stats.get('avg_ann_time', 0):.4f}s")
        print(f"    Linear Time: {stats.get('avg_linear_time', 0):.4f}s")


def main():
    """主函数"""
    print("=== 生成清晰的性能对比图表 ===")
    
    # 加载结果
    results = load_results()
    if not results:
        print("无法加载测试结果")
        return
    
    # 打印汇总
    print_performance_summary(results)
    
    # 生成详细图表
    print("\n生成详细性能图表...")
    create_performance_plots(results)
    
    # 生成汇总图表
    print("\n生成汇总图表...")
    create_summary_plot(results)
    
    print("\n✅ 所有图表生成完成！")
    print("文件:")
    print("  - algorithm_comparison_clean.png (详细对比)")
    print("  - algorithm_summary_clean.png (汇总指标)")


if __name__ == "__main__":
    main()
