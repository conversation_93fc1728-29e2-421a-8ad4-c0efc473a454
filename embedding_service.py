"""
Ollama Embedding服务接口
"""
import requests
import json
import numpy as np
from typing import List, Union
from config import OLLAMA_BASE_URL, EMBEDDING_MODEL


class OllamaEmbeddingService:
    """Ollama embedding服务客户端"""
    
    def __init__(self, base_url: str = OLLAMA_BASE_URL, model: str = EMBEDDING_MODEL):
        self.base_url = base_url
        self.model = model
        self.embed_url = f"{base_url}/api/embeddings"
    
    def get_embedding(self, text: str) -> List[float]:
        """
        获取单个文本的embedding向量
        
        Args:
            text: 输入文本
            
        Returns:
            embedding向量列表
        """
        payload = {
            "model": self.model,
            "prompt": text
        }
        
        try:
            response = requests.post(self.embed_url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            return result.get("embedding", [])
            
        except requests.exceptions.RequestException as e:
            print(f"请求Ollama API失败: {e}")
            return []
        except json.JSONDecodeError as e:
            print(f"解析响应JSON失败: {e}")
            return []
    
    def get_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """
        批量获取多个文本的embedding向量
        
        Args:
            texts: 文本列表
            
        Returns:
            embedding向量列表的列表
        """
        embeddings = []
        for text in texts:
            embedding = self.get_embedding(text)
            if embedding:
                embeddings.append(embedding)
            else:
                print(f"获取文本embedding失败: {text[:50]}...")
                
        return embeddings
    
    def test_connection(self) -> bool:
        """
        测试与Ollama服务的连接
        
        Returns:
            连接是否成功
        """
        try:
            test_text = "Hello, world!"
            embedding = self.get_embedding(test_text)
            return len(embedding) > 0
        except Exception as e:
            print(f"连接测试失败: {e}")
            return False
    
    def get_model_info(self) -> dict:
        """
        获取模型信息
        
        Returns:
            模型信息字典
        """
        try:
            response = requests.get(f"{self.base_url}/api/tags")
            response.raise_for_status()
            
            models = response.json().get("models", [])
            for model in models:
                if model.get("name", "").startswith(self.model):
                    return model
            
            return {}
            
        except Exception as e:
            print(f"获取模型信息失败: {e}")
            return {}


def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """
    计算两个向量的余弦相似度
    
    Args:
        vec1: 向量1
        vec2: 向量2
        
    Returns:
        余弦相似度值 (-1到1之间)
    """
    if not vec1 or not vec2:
        return 0.0
    
    vec1_np = np.array(vec1)
    vec2_np = np.array(vec2)
    
    # 计算余弦相似度
    dot_product = np.dot(vec1_np, vec2_np)
    norm1 = np.linalg.norm(vec1_np)
    norm2 = np.linalg.norm(vec2_np)
    
    if norm1 == 0 or norm2 == 0:
        return 0.0
    
    return dot_product / (norm1 * norm2)


if __name__ == "__main__":
    # 测试embedding服务
    service = OllamaEmbeddingService()
    
    print("测试Ollama连接...")
    if service.test_connection():
        print("✅ 连接成功!")
        
        # 获取模型信息
        model_info = service.get_model_info()
        if model_info:
            print(f"模型信息: {model_info.get('name', 'Unknown')}")
            print(f"模型大小: {model_info.get('size', 'Unknown')}")
        
        # 测试embedding
        test_texts = [
            "人工智能是计算机科学的一个分支",
            "机器学习是人工智能的重要组成部分",
            "今天天气很好，适合出去散步"
        ]
        
        print("\n测试embedding生成...")
        embeddings = service.get_embeddings_batch(test_texts)
        
        if embeddings:
            print(f"成功生成 {len(embeddings)} 个embedding向量")
            print(f"向量维度: {len(embeddings[0])}")
            
            # 测试相似度计算
            print("\n相似度测试:")
            sim1 = cosine_similarity(embeddings[0], embeddings[1])
            sim2 = cosine_similarity(embeddings[0], embeddings[2])
            
            print(f"文本1与文本2的相似度: {sim1:.4f}")
            print(f"文本1与文本3的相似度: {sim2:.4f}")
            
    else:
        print("❌ 连接失败! 请确保Ollama服务正在运行")
